{"SLICE": {"code": "import { {{INTERFACESTATE}} } from \"@/interfaces/{{INTERFACE_FOLDER}}{{INTERFACE_FILE_NAME}}\";\nimport { createSlice } from \"@reduxjs/toolkit\";\n\nconst initialState: {{INTERFACESTATE}} = {\n  {{PREFIX}}DetailData: {},\n  {{PREFIX}}ListData: [],\n  {{PREFIX}}DropdownData: [],\n};\n\nconst {{PREFIX}}eSlice = createSlice({\n  name: \"{{SLICE}}\",\n  initialState,\n  reducers: {\n    {{PREFIX}}DetailSlice: (state, action) => {\n      state.{{PREFIX}}DetailData = action?.payload;\n    },\n    {{PREFIX}}ListSlice: (state, action) => {\n      state.{{PREFIX}}ListData = action?.payload;\n    },\n    {{PREFIX}}DropdownSlice: (state, action) => {\n      state.{{PREFIX}}DropdownData = action?.payload;\n    },\n    clear{{PREFIX}}DetailSlice: (state) => {\n      state.{{PREFIX}}DetailData = null;\n    },\n  },\n});\n\nexport const {\n  {{PREFIX}}DetailSlice,\n  {{PREFIX}}ListSlice,\n  {{PREFIX}}DropdownSlice,\n  clear{{PREFIX}}DetailSlice,\n} = {{SLICE}}Slice.actions;\n\nexport default {{SLICE}}Slice.reducer;", "created": "2025-07-09T16:32:21.460371", "placeholders": ["PREFIX", "SLICE", "INTERFACE_FILE_NAME", "INTERFACE_FOLDER", "INTERFACESTATE"]}}